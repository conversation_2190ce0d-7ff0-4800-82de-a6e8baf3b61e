<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F8F8F8"
    android:orientation="vertical">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="80dp"
        app:layout_constraintTop_toTopOf="parent">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/back"
            android:layout_marginTop="20dp"
            android:layout_width="wrap_content"
            android:padding="15dp"
            android:layout_height="wrap_content">
            <ImageView
                android:id="@+id/ivBack"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toBottomOf="@id/back"
                app:layout_constraintStart_toStartOf="@id/back"
                android:src="@drawable/ic_chevron_arrow_back"/>

            <TextView
                android:id="@+id/tvBack"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="16sp"
                android:fontFamily="@font/inter_regular"
                android:textColor="@color/orangemuda"
                android:textStyle="bold"
                android:layout_marginStart="15dp"
                app:layout_constraintStart_toEndOf="@id/ivBack"
                app:layout_constraintTop_toTopOf="@id/ivBack"
                app:layout_constraintBottom_toBottomOf="@id/ivBack"
                android:text="Daftar Surat"/>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.appcompat.widget.Toolbar>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:paddingStart="16dp"
            android:paddingEnd="16dp"
            android:paddingBottom="16dp">

            <androidx.cardview.widget.CardView
                android:id="@+id/cv_stats"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardBackgroundColor="#FFFFFF"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="🌱"
                            android:textSize="22sp"
                            android:layout_marginEnd="12dp" />

                        <LinearLayout
                            android:id="@+id/perjalanan_count"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <TextView
                                android:id="@+id/perjalanan"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textColor="#444444"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:layout_marginBottom="8dp" />

                            <RelativeLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content">

                                <com.google.android.material.progressindicator.LinearProgressIndicator
                                    android:id="@+id/progressHafalan"
                                    android:layout_width="match_parent"
                                    android:layout_height="12dp"
                                    app:trackCornerRadius="6dp"
                                    app:trackThickness="12dp"
                                    app:indicatorColor="@color/orangemuda"
                                    app:trackColor="#EEEEEE" />

                                <TextView
                                    android:id="@+id/tvProgressPercent"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_alignParentEnd="true"
                                    android:layout_marginTop="16dp"
                                    android:textSize="12sp"
                                    android:textColor="@color/orangemuda"
                                    android:textStyle="bold" />
                            </RelativeLayout>
                        </LinearLayout>
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:paddingTop="5dp"
                android:clipChildren="false"
                android:clipToPadding="false">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:translationZ="10dp"
                    android:background="@color/white"
                    android:id="@+id/shimmerAudioSurat">
                    <com.airbnb.lottie.LottieAnimationView
                        android:layout_width="100dp"
                        android:layout_height="100dp"
                        app:lottie_loop="true"
                        app:lottie_autoPlay="true"
                        app:lottie_rawRes="@raw/loadorange"/>
                    <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="Memuat Data..."
                        android:textStyle="bold"
                        android:textColor="@color/orangemuda"
                        android:gravity="center"/>
                </LinearLayout>

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_data"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    tools:listitem="@layout/daftar_surat_challenge"
                    android:nestedScrollingEnabled="true"
                    android:clipChildren="false"
                    android:clipToPadding="false"
                    android:visibility="gone"/>
            </RelativeLayout>
        </LinearLayout>
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
</LinearLayout>