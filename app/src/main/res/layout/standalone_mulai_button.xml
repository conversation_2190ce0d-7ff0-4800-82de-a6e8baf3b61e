<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <androidx.cardview.widget.CardView
        android:id="@+id/floating_mulai_card"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:elevation="999dp"
        app:cardBackgroundColor="@color/orangemuda"
        app:cardCornerRadius="20dp"
        app:cardElevation="20dp">

        <TextView
            android:id="@+id/floating_mulai_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingLeft="16dp"
            android:paddingTop="8dp"
            android:paddingRight="16dp"
            android:paddingBottom="8dp"
            android:text="Mulai"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:textStyle="bold" />
    </androidx.cardview.widget.CardView>

    <!-- Triangle pointer -->
    <View
        android:id="@+id/triangle_pointer"
        android:layout_width="16dp"
        android:layout_height="8dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="-4dp"
        android:background="@drawable/triangle_pointer" />

</LinearLayout>
