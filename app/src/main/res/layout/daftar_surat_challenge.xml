<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:clipToPadding="false">

    <!-- Main content in a LinearLayout -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/left_track"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="-10dp">

            <RelativeLayout
                android:id="@+id/main_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="100dp"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/circle"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:src="@drawable/left_this_active" />
            </LinearLayout>


            <LinearLayout
                android:id="@+id/layout_left"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="10dp">

                <ImageView
                    android:id="@+id/btn_surat"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_gravity="center_vertical"
                    android:scaleType="centerCrop" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginLeft="16dp"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/urutan_audio"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text=""
                        android:textColor="#777777"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/ayat"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text=""
                        android:textColor="#333333"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:id="@+id/tandai_hafal_left"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:background="@drawable/bg_pressed_menu"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <ImageView
                            android:id="@+id/ic_tandai_hafal_left"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content" />

                        <TextView
                            android:id="@+id/text_hafal_left"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:textSize="12dp" />
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>


            <LinearLayout
                android:id="@+id/layout_right"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginRight="20dp"
                android:gravity="right">

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginRight="16dp"
                    android:gravity="right"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/urutan_audio2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text=""
                        android:textColor="#777777"
                        android:textSize="14sp" />

                    <TextView
                        android:id="@+id/ayat2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text=""
                        android:textColor="#333333"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <LinearLayout
                        android:id="@+id/tandai_hafal_right"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:background="@drawable/bg_pressed_menu"
                        android:gravity="center"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/text_hafal_right"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:textSize="12dp" />

                        <ImageView
                            android:id="@+id/ic_tandai_hafal_right"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content" />
                    </LinearLayout>
                </LinearLayout>

                <ImageView
                    android:id="@+id/btn_surat2"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:layout_gravity="center_vertical"
                    android:scaleType="centerCrop" />
            </LinearLayout>
        </RelativeLayout>
    </LinearLayout>
</LinearLayout>

    <!-- Mulai Button - positioned on top of everything with maximum z-index -->
    <ImageView
        android:id="@+id/mulai_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:adjustViewBounds="true"
        android:scaleType="fitCenter"
        android:elevation="999999dp"
        android:translationZ="999999dp"
        android:visibility="gone"
        android:background="@android:color/transparent"
        android:contentDescription="Mulai" />
</FrameLayout>